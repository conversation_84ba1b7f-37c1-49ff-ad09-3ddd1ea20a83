import Link from "next/link";
import type { FC } from "react";
import type { Page } from "@/payload-types";

import { cn } from "@/lib/utils";

type CMSLinkType = {
	appearance?: "inline";
	children?: React.ReactNode;
	className?: string;
	label?: string | null;
	newTab?: boolean | null;
	reference?: {
		relationTo: "pages" | "posts";
		value: Page | string | number;
	} | null;
	type?: "custom" | "reference" | null;
	url?: string | null;
};

export const CMSLink: FC<CMSLinkType> = (props) => {
	const {
		type,
		appearance = "inline",
		children,
		className,
		label,
		newTab,
		reference,
		url,
	} = props;

	const href =
		type === "reference" &&
		typeof reference?.value === "object" &&
		reference.value.slug
			? `${reference?.relationTo !== "pages" ? `/${reference?.relationTo}` : ""}/${
					reference.value.slug
				}`
			: url;

	if (!href) return null;

	const newTabProps = newTab
		? { rel: "noopener noreferrer", target: "_blank" }
		: {};

	if (appearance === "inline") {
		return (
			<Link className={cn(className)} href={href || url || ""} {...newTabProps}>
				{label && label}
				{children && children}
			</Link>
		);
	}

	return (
		<Link className={cn(className)} href={href || url || ""} {...newTabProps}>
			{label && label}
			{children && children}
		</Link>
	);
};
