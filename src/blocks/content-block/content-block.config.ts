import type { Block, Field } from "payload";

import {
	FixedToolbarFeature,
	HeadingFeature,
	InlineToolbarFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";
import { link } from "@/fields/link";

const columnFields: Field[] = [
	{
		name: "richText",
		type: "richText",
		editor: lexicalEditor({
			features: ({ rootFeatures }) => {
				return [
					...rootFeatures,
					HeadingFeature({ enabledHeadingSizes: ["h2", "h3", "h4"] }),
					FixedToolbarFeature(),
					InlineToolbarFeature(),
				];
			},
		}),
		label: false,
	},
	{
		name: "enableLink",
		type: "checkbox",
	},
	link({
		overrides: {
			admin: {
				condition: (_data, siblingData) => {
					return Boolean(siblingData?.enableLink);
				},
			},
		},
	}),
];

export const ContentBlockConfig: Block = {
	slug: "content",
	interfaceName: "ContentBlock",
	fields: [
		{
			name: "indent",
			type: "select",
			defaultValue: "oneSixth",
			options: [
				{
					label: "One Sixth",
					value: "oneSixth",
				},
				{
					label: "One Third",
					value: "oneThird",
				},
				{
					label: "Half",
					value: "half",
				},
			],
		},
		{
			name: "columns",
			type: "array",
			admin: {
				initCollapsed: true,
			},
			fields: columnFields,
		},
	],
};
