import type { FC } from "react";
import type { ContentBlock as ContentBlockProps } from "@/payload-types";

import { cn } from "@/lib/utils";
import RichText from "@/components/render/render-rich-text";
import { Link } from "@/components/link";
import { CMSLink } from "@/components/cms-link";

export const ContentBlock: FC<ContentBlockProps> = (props) => {
	const { columns, indent, id } = props;

	const indentClasses = {
		oneSixth: "lg:col-span-2",
		oneThird: "lg:col-span-4",
		half: "lg:col-span-6",
	};

	const colSpanClasses = {
		oneSixth: "lg:col-span-8",
		oneThird: "lg:col-span-6",
		half: "lg:col-span-6",
	};

	return (
		<section className="layout-block">
			<div className="default-grid">
				{indent && (
					<div
						className={cn(
							"col-start-1",
							indentClasses[indent as keyof typeof indentClasses],
						)}
					/>
				)}
				{columns &&
					columns.length > 0 &&
					columns.map((col, index) => {
						const { richText, enableLink, link } = col;

						return (
							<div
								className={cn(
									"col-span-4",
									colSpanClasses[indent as keyof typeof colSpanClasses],
								)}
								key={`${id}-${index * 1}`}
							>
								{richText && <RichText data={richText} enableGutter={false} />}
								{enableLink && <CMSLink {...link} />}
							</div>
						);
					})}
			</div>
		</section>
	);
};
