"use client";

import type { <PERSON> } from "react";
import type { Page } from "@/payload-types";

export const HighImpact: FC<Page["hero"]> = ({ content, title }) => {
	return (
		<section className="relative h-[80svh] bg-primary pt-[calc(var(--gap)*2)]">
			<div className="dr-layout-grid h-full py-gap">
				<h1 className="dr-text-78 col-span-8 col-start-5 indent-[25%] uppercase">{title}</h1>
				<p className="col-span-6 col-start-1 text-balance content-end-safe">{content}</p>
			</div>
		</section>
	);
};
